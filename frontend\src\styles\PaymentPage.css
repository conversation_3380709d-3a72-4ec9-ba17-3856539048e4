/* PaymentPage.css */

.payment-page {
  min-height: 100vh;
  background-color: #f8fafc;
}

/* Header styles */
.header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 0;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-bg {
  text-decoration: none;
  color: #041b4b;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-star {
  color: #f59e0b;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-left: auto;
}

.header-row {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-input {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  width: 300px;
}

.icons-area {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.shopping-bag-rel {
  position: relative;
}

.shopping-bag-badge {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: #dc2626;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
}

.login-link, .register-link {
  text-decoration: none;
  color: #374151;
  font-weight: bold;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.login-link:hover, .register-link:hover {
  background-color: #f3f4f6;
}

/* Category Navigation */
.category-nav {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.5rem 0;
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.category-group {
  position: relative;
}

.category-btn {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  cursor: pointer;
  color: #374151;
  text-decoration: none;
  transition: color 0.2s;
}

.category-btn:hover {
  color: #041b4b;
}

.category-btn.women {
  color: #041b4b;
  font-weight: bold;
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 150px;
}

.dropdown ul {
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
}

.dropdown li {
  padding: 0;
}

.dropdown a {
  display: block;
  padding: 0.5rem 1rem;
  color: #374151;
  text-decoration: none;
  transition: background-color 0.2s;
}

.dropdown a:hover {
  background-color: #f3f4f6;
}

/* Back Navigation */
.back-navigation {
  padding: 1rem 2rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 0.9rem;
  transition: color 0.2s;
}

.back-btn:hover {
  color: #374151;
}

/* Main Payment Container */
.payment-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.payment-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 3rem;
  align-items: start;
}

/* Payment Form Section */
.payment-form-section {
  background: white;
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.payment-form-section h2 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1.5rem;
}

/* Trust Indicators */
.trust-indicators {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #10b981;
  font-size: 0.9rem;
}

/* Form Styles */
.payment-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.form-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Payment Methods */
.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: border-color 0.2s, background-color 0.2s;
}

.payment-option:hover {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.payment-option.selected {
  border-color: #2563eb;
  background-color: #eff6ff;
}

.payment-option input[type="radio"] {
  margin: 0;
}

/* Card Details */
.card-details {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
}

/* Place Order Button */
.place-order-btn {
  background: #2563eb;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 1rem;
}

.place-order-btn:hover:not(:disabled) {
  background: #1d4ed8;
}

.place-order-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Order Summary Section */
.order-summary-section {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 2rem;
}

.order-summary-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
}

/* Product Summary */
.product-summary {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 0.5rem;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-details {
  flex: 1;
}

.product-details h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.sale-price {
  font-weight: 600;
  color: #dc2626;
  font-size: 1.125rem;
}

.original-price {
  text-decoration: line-through;
  color: #6b7280;
  font-size: 0.9rem;
}

/* Product Options */
.product-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.option-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.option-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  min-width: 60px;
}

.option-group select {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.quantity-controls button {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  width: 32px;
  height: 32px;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.quantity-controls button:hover:not(:disabled) {
  background: #e5e7eb;
}

.quantity-controls button:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.quantity-controls span {
  min-width: 30px;
  text-align: center;
  font-weight: 500;
}

/* Price Breakdown */
.price-breakdown {
  margin-bottom: 1.5rem;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 0.9rem;
}

.price-row.savings {
  color: #10b981;
}

.price-row.total {
  font-weight: 600;
  font-size: 1.125rem;
  color: #111827;
  border-top: 1px solid #e5e7eb;
  margin-top: 0.5rem;
  padding-top: 1rem;
}

.price-breakdown hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 0.5rem 0;
}

/* Delivery Info */
.delivery-info {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
}

.delivery-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
}

.delivery-info p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0;
}

/* Order Success */
.order-success {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.success-container {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  max-width: 500px;
}

.success-icon {
  margin-bottom: 1.5rem;
}

.success-container h1 {
  font-size: 2rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1rem;
}

.success-container p {
  color: #6b7280;
  margin-bottom: 2rem;
}

.order-details {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.order-details h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.order-details p {
  margin: 0.5rem 0;
  color: #374151;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.continue-shopping-btn,
.view-orders-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  text-decoration: none;
  display: inline-block;
}

.continue-shopping-btn {
  background: #2563eb;
  color: white;
  border: none;
}

.continue-shopping-btn:hover {
  background: #1d4ed8;
}

.view-orders-btn {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.view-orders-btn:hover {
  background: #f9fafb;
}

/* Loading */
.loading-container {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading {
  font-size: 1.125rem;
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .order-summary-section {
    position: static;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .trust-indicators {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .category-nav {
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
  }
  
  .payment-container {
    padding: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}

/* Enhanced Order Summary Styles */
.order-summary-section.enhanced {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  min-width: 400px;
}

.order-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f3f4f6;
}

.order-summary-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.summary-badge {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.item-count {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Enhanced Product Summary */
.product-summary.enhanced {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
}

.product-image-container {
  position: relative;
  margin-bottom: 1rem;
}

.product-image-large {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.sale-badge-summary {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #dc2626;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.product-details-enhanced {
  text-align: center;
}

.product-name-large {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.product-price-display {
  margin-bottom: 1.5rem;
}

.current-price-large {
  font-size: 1.5rem;
  font-weight: 700;
  color: #dc2626;
  margin-right: 0.75rem;
}

.original-price-crossed {
  font-size: 1.125rem;
  color: #6b7280;
  text-decoration: line-through;
}

/* Enhanced Product Options */
.product-options-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.option-group-enhanced {
  text-align: left;
}

.option-label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Color Selector */
.color-selector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  background: none;
}

.color-option.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.color-option:hover {
  transform: scale(1.1);
}

.selected-option {
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

/* Size Selector */
.size-selector {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.size-option {
  padding: 0.5rem 1rem;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  min-width: 44px;
  text-align: center;
}

.size-option.selected {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
}

.size-option:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

/* Enhanced Quantity Controls */
.quantity-section {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

.quantity-controls-enhanced {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 0.75rem 0;
}

.quantity-btn {
  width: 44px;
  height: 44px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-weight: 600;
}

.quantity-btn:hover:not(:disabled) {
  border-color: #3b82f6;
  background: #eff6ff;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-btn.increase {
  border-color: #10b981;
  color: #10b981;
}

.quantity-btn.increase:hover:not(:disabled) {
  background: #ecfdf5;
  border-color: #059669;
}

.quantity-btn.decrease {
  border-color: #ef4444;
  color: #ef4444;
}

.quantity-btn.decrease:hover:not(:disabled) {
  background: #fef2f2;
  border-color: #dc2626;
}

.quantity-display {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  min-width: 60px;
  text-align: center;
}

.quantity-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
}

.item-total-display {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 0.75rem;
  text-align: center;
  margin-top: 0.75rem;
}

.item-total-label {
  display: block;
  font-size: 0.875rem;
  color: #0369a1;
  margin-bottom: 0.25rem;
}

.item-total-price {
  font-size: 1.125rem;
  font-weight: 700;
  color: #0c4a6e;
}

/* Enhanced Price Breakdown */
.price-breakdown-enhanced {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
}

.breakdown-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
  text-align: center;
}

.price-row-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f5f9;
}

.price-row-enhanced:last-child {
  border-bottom: none;
}

.price-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4b5563;
}

.price-value {
  font-weight: 600;
  color: #1f2937;
}

.price-row-enhanced.savings {
  background: #f0fdf4;
  border-radius: 6px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  border: 1px solid #bbf7d0;
}

.savings-label {
  color: #059669;
  font-weight: 600;
}

.savings-value {
  color: #059669;
  font-weight: 700;
}

.savings-icon, .delivery-icon {
  font-size: 1rem;
}

.price-divider {
  height: 2px;
  background: linear-gradient(90deg, #e5e7eb, #d1d5db, #e5e7eb);
  margin: 1rem 0;
  border-radius: 1px;
}

.total-row {
  background: #1f2937;
  color: white;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.total-label {
  font-size: 1.125rem;
  font-weight: 700;
  color: white;
}

.total-value {
  font-size: 1.5rem;
  font-weight: 800;
  color: #fbbf24;
}

.savings-summary {
  text-align: center;
  margin-top: 1rem;
}

.total-savings {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
  display: inline-block;
}

/* Enhanced Payment Methods */
.payment-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.payment-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0.5rem 0 1.5rem 0;
}

.payment-methods-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.payment-option-enhanced {
  display: block;
  cursor: pointer;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 0;
  background: white;
  transition: all 0.3s ease;
  overflow: hidden;
}

.payment-option-enhanced:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.payment-option-enhanced.selected {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.payment-option-enhanced input[type="radio"] {
  display: none;
}

.payment-option-content-enhanced {
  display: flex;
  align-items: center;
  padding: 1.25rem;
  gap: 1rem;
}

.payment-icon-container {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.payment-option-enhanced.selected .payment-icon-container {
  background: #3b82f6;
  border-color: #3b82f6;
}

.payment-option-enhanced.selected .payment-method-icon {
  color: white;
}

.payment-icon-container.paypal {
  background: #0070ba;
  border-color: #0070ba;
}

.payment-icon-container.cod {
  background: #f59e0b;
  border-color: #f59e0b;
}

.payment-icon-emoji {
  font-size: 1.5rem;
}

.payment-method-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.payment-method-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.payment-method-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
}

.card-logos-enhanced {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.card-logo {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.visa-enhanced {
  background: #1a1f71;
  color: white;
}

.mastercard-enhanced {
  background: #eb001b;
  color: white;
}

.amex-enhanced {
  background: #006fcf;
  color: white;
}

.payment-check {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .order-summary-section.enhanced {
    min-width: unset;
    padding: 1.5rem;
  }

  .order-summary-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .product-image-large {
    height: 150px;
  }

  .quantity-controls-enhanced {
    gap: 0.75rem;
  }

  .quantity-btn {
    width: 40px;
    height: 40px;
  }

  .payment-option-content-enhanced {
    padding: 1rem;
    gap: 0.75rem;
  }

  .payment-icon-container {
    width: 40px;
    height: 40px;
  }
}
