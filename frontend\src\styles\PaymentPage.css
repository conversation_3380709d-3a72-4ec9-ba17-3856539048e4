/* PaymentPage.css */

.payment-page {
  min-height: 100vh;
  background-color: #f8fafc;
}

/* Header styles */
.header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 0;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-bg {
  text-decoration: none;
  color: #041b4b;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-star {
  color: #f59e0b;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-left: auto;
}

.header-row {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-input {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  width: 300px;
}

.icons-area {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.shopping-bag-rel {
  position: relative;
}

.shopping-bag-badge {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: #dc2626;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
}

.login-link, .register-link {
  text-decoration: none;
  color: #374151;
  font-weight: bold;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.login-link:hover, .register-link:hover {
  background-color: #f3f4f6;
}

/* Category Navigation */
.category-nav {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.5rem 0;
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.category-group {
  position: relative;
}

.category-btn {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  cursor: pointer;
  color: #374151;
  text-decoration: none;
  transition: color 0.2s;
}

.category-btn:hover {
  color: #041b4b;
}

.category-btn.women {
  color: #041b4b;
  font-weight: bold;
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 150px;
}

.dropdown ul {
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
}

.dropdown li {
  padding: 0;
}

.dropdown a {
  display: block;
  padding: 0.5rem 1rem;
  color: #374151;
  text-decoration: none;
  transition: background-color 0.2s;
}

.dropdown a:hover {
  background-color: #f3f4f6;
}

/* Back Navigation */
.back-navigation {
  padding: 1rem 2rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 0.9rem;
  transition: color 0.2s;
}

.back-btn:hover {
  color: #374151;
}

/* Main Payment Container */
.payment-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.payment-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 3rem;
  align-items: start;
}

/* Payment Form Section */
.payment-form-section {
  background: white;
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.payment-form-section h2 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1.5rem;
}

/* Trust Indicators */
.trust-indicators {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #10b981;
  font-size: 0.9rem;
}

/* Form Styles */
.payment-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.form-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Payment Methods */
.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: border-color 0.2s, background-color 0.2s;
}

.payment-option:hover {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.payment-option.selected {
  border-color: #2563eb;
  background-color: #eff6ff;
}

.payment-option input[type="radio"] {
  margin: 0;
}

/* Card Details */
.card-details {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
}

/* Place Order Button */
.place-order-btn {
  background: #2563eb;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 1rem;
}

.place-order-btn:hover:not(:disabled) {
  background: #1d4ed8;
}

.place-order-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Order Summary Section */
.order-summary-section {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 2rem;
}

.order-summary-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
}

/* Product Summary */
.product-summary {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 0.5rem;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-details {
  flex: 1;
}

.product-details h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.sale-price {
  font-weight: 600;
  color: #dc2626;
  font-size: 1.125rem;
}

.original-price {
  text-decoration: line-through;
  color: #6b7280;
  font-size: 0.9rem;
}

/* Product Options */
.product-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.option-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.option-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  min-width: 60px;
}

.option-group select {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.quantity-controls button {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  width: 32px;
  height: 32px;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.quantity-controls button:hover:not(:disabled) {
  background: #e5e7eb;
}

.quantity-controls button:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.quantity-controls span {
  min-width: 30px;
  text-align: center;
  font-weight: 500;
}

/* Price Breakdown */
.price-breakdown {
  margin-bottom: 1.5rem;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 0.9rem;
}

.price-row.savings {
  color: #10b981;
}

.price-row.total {
  font-weight: 600;
  font-size: 1.125rem;
  color: #111827;
  border-top: 1px solid #e5e7eb;
  margin-top: 0.5rem;
  padding-top: 1rem;
}

.price-breakdown hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 0.5rem 0;
}

/* Delivery Info */
.delivery-info {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
}

.delivery-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
}

.delivery-info p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0;
}

/* Order Success */
.order-success {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.success-container {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  max-width: 500px;
}

.success-icon {
  margin-bottom: 1.5rem;
}

.success-container h1 {
  font-size: 2rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1rem;
}

.success-container p {
  color: #6b7280;
  margin-bottom: 2rem;
}

.order-details {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.order-details h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.order-details p {
  margin: 0.5rem 0;
  color: #374151;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.continue-shopping-btn,
.view-orders-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  text-decoration: none;
  display: inline-block;
}

.continue-shopping-btn {
  background: #2563eb;
  color: white;
  border: none;
}

.continue-shopping-btn:hover {
  background: #1d4ed8;
}

.view-orders-btn {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.view-orders-btn:hover {
  background: #f9fafb;
}

/* Loading */
.loading-container {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading {
  font-size: 1.125rem;
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .order-summary-section {
    position: static;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .trust-indicators {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .category-nav {
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
  }
  
  .payment-container {
    padding: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
