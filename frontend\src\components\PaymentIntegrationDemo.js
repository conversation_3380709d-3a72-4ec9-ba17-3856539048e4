// PaymentIntegrationDemo.js - Complete React Component Example
import React, { useState } from 'react';
import Payment from './Payment';

// Sample product data structure for testing
const sampleProducts = [
  {
    id: 1,
    name: "Classic Cotton T-Shirt",
    originalPrice: 29.99,
    salePrice: 19.99,
    discount: 33,
    image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop&crop=center",
    colors: ['black', 'white', 'navy'],
    sizes: ['XS', 'S', 'M', 'L', 'XL'],
    sku: 'CCT001',
    brand: 'StyleStore',
    material: '100% Cotton',
    description: 'Comfortable classic cotton t-shirt perfect for everyday wear'
  },
  {
    id: 2,
    name: "Running Sneakers",
    originalPrice: 89.99,
    salePrice: 59.99,
    discount: 33,
    image: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop&crop=center",
    colors: ['white', 'black', 'grey'],
    sizes: ['6', '7', '8', '9', '10', '11'],
    sku: 'RS002',
    brand: 'SportStore',
    material: 'Synthetic',
    description: 'High-performance running shoes with excellent cushioning'
  }
];

const PaymentIntegrationDemo = () => {
  // 1. State for payment modal
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isPaymentOpen, setIsPaymentOpen] = useState(false);

  // 2. Click handlers
  const handleProductClick = (product) => {
    setSelectedProduct(product);
    setIsPaymentOpen(true);
  };

  const closePayment = () => {
    setIsPaymentOpen(false);
    setSelectedProduct(null);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1 style={{ textAlign: 'center', marginBottom: '30px', color: '#333' }}>
        Payment Integration Demo
      </h1>
      
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
        gap: '20px',
        marginBottom: '40px'
      }}>
        {sampleProducts.map((product) => (
          <div key={product.id} style={{
            border: '1px solid #ddd',
            borderRadius: '12px',
            padding: '20px',
            backgroundColor: 'white',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            transition: 'transform 0.2s',
            cursor: 'pointer'
          }}
          onMouseEnter={(e) => e.target.style.transform = 'translateY(-2px)'}
          onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
          >
            {/* 3. Product image with click handler */}
            <img 
              src={product.image} 
              alt={product.name} 
              className="product-images"
              onClick={() => handleProductClick(product)}
              style={{ 
                width: '100%', 
                height: '200px', 
                objectFit: 'cover', 
                borderRadius: '8px',
                cursor: 'pointer',
                marginBottom: '15px'
              }}
            />
            
            <h3 style={{ margin: '10px 0', color: '#333' }}>{product.name}</h3>
            <p style={{ color: '#666', fontSize: '14px', marginBottom: '10px' }}>
              {product.description}
            </p>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px' }}>
              <span style={{ fontSize: '18px', fontWeight: 'bold', color: '#e53e3e' }}>
                ${product.salePrice}
              </span>
              <span style={{ 
                fontSize: '14px', 
                color: '#999', 
                textDecoration: 'line-through' 
              }}>
                ${product.originalPrice}
              </span>
              <span style={{ 
                backgroundColor: '#e53e3e', 
                color: 'white', 
                padding: '2px 6px', 
                borderRadius: '4px', 
                fontSize: '12px' 
              }}>
                {product.discount}% OFF
              </span>
            </div>
            
            <div style={{ fontSize: '12px', color: '#888' }}>
              SKU: {product.sku} | Brand: {product.brand}
            </div>
            
            <button 
              onClick={() => handleProductClick(product)}
              style={{
                width: '100%',
                padding: '10px',
                marginTop: '15px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              Click to Purchase
            </button>
          </div>
        ))}
      </div>

      {/* Integration Instructions */}
      <div style={{ 
        backgroundColor: '#f8f9fa', 
        padding: '30px', 
        borderRadius: '12px',
        marginTop: '40px'
      }}>
        <h2 style={{ color: '#333', marginBottom: '20px' }}>Integration Instructions</h2>
        
        <div style={{ marginBottom: '30px' }}>
          <h3 style={{ color: '#555', marginBottom: '15px' }}>Quick Setup Steps:</h3>
          <ol style={{ lineHeight: '1.8', color: '#666' }}>
            <li>Copy <code>Payment.jsx</code> and <code>Payment.css</code> to your components folder</li>
            <li>Import Payment component: <code>import Payment from './Payment';</code></li>
            <li>Add state variables for modal control</li>
            <li>Add click handlers for opening/closing modal</li>
            <li>Add onClick handler to product images</li>
            <li>Include Payment component in your JSX</li>
            <li>Ensure your product data has all required fields</li>
          </ol>
        </div>

        <div style={{ marginBottom: '30px' }}>
          <h3 style={{ color: '#555', marginBottom: '15px' }}>Required Product Data Structure:</h3>
          <pre style={{ 
            backgroundColor: '#fff', 
            padding: '15px', 
            borderRadius: '8px',
            border: '1px solid #ddd',
            fontSize: '13px',
            overflow: 'auto'
          }}>
{`const product = {
  id: 1,
  name: "Product Name",
  originalPrice: 100.00,
  salePrice: 50.00,
  discount: 50,
  image: "image-url",
  colors: ['black', 'white', 'navy'],
  sizes: ['XS', 'S', 'M', 'L', 'XL'], // or ['6', '7', '8'] for shoes
  sku: 'SKU001',
  brand: 'Brand Name',
  material: 'Material',
  description: 'Product description'
};`}
          </pre>
        </div>

        <div>
          <h3 style={{ color: '#555', marginBottom: '15px' }}>Testing:</h3>
          <ul style={{ lineHeight: '1.8', color: '#666' }}>
            <li>Click on any product image above to test the payment modal</li>
            <li>Test color and size selection functionality</li>
            <li>Verify quantity controls and price calculations</li>
            <li>Check modal opening/closing behavior</li>
            <li>Test responsive design on different screen sizes</li>
          </ul>
        </div>
      </div>

      {/* 4. Payment modal component */}
      <Payment 
        product={selectedProduct}
        isOpen={isPaymentOpen}
        onClose={closePayment}
      />
    </div>
  );
};

export default PaymentIntegrationDemo;

/*
INTEGRATION TEMPLATE FOR EXISTING COMPONENTS:

// 1. Import at the top of your component file
import React, { useState } from 'react';
import Payment from './Payment';

// 2. Add inside your component function
const [selectedProduct, setSelectedProduct] = useState(null);
const [isPaymentOpen, setIsPaymentOpen] = useState(false);

const handleProductClick = (product) => {
  setSelectedProduct(product);
  setIsPaymentOpen(true);
};

const closePayment = () => {
  setIsPaymentOpen(false);
  setSelectedProduct(null);
};

// 3. Update your product image JSX
<img 
  src={product.image} 
  alt={product.name} 
  className="product-images" 
  onClick={() => handleProductClick(product)}
  style={{ cursor: 'pointer' }}
/>

// 4. Add before your component's closing tag
<Payment 
  product={selectedProduct}
  isOpen={isPaymentOpen}
  onClose={closePayment}
/>
*/
