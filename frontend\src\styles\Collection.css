.collections-container {
  display: flex;
  flex-direction: column;
  .pr.products-horizontal-grid {
  display: flex;
  gap: 2.5rem; /* Increased gap for better spacing with larger cards */
  padding: 0 1rem;
  min-width: max-content;
}card-horizontal {
  min-width: 350px; /* Increased from 300px */
  max-width: 350px;
  flex: 0 0 auto;
  background-color: white;
  border-radius: 0.75rem; /* Slightly more rounded */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translateY(0);
  scroll-snap-align: start;
}}

.collection-section {
  width: 90%;
  margin: auto;
  max-width: 1400px; /* Limit max width for better layout control */
}

.collection-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ebdcdc;
  background-color: #041b4b;
  text-align: center;
  width: 30%;
  margin: auto;
  border-radius: 20px;
}

/* Original grid layout (keeping for backward compatibility) */
.products-grid {
  display: flex;
  flex-direction: row;
  gap: 3rem;
  overflow-x: auto;
  padding-bottom: 1rem;
  scroll-snap-type: x mandatory;
  margin-top: 3rem;
}

/* New horizontal scrolling layout */
.products-horizontal-scroll {
  margin-top: 3rem;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 1rem;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
  
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: #041b4b #f1f1f1;
}

.products-horizontal-scroll::-webkit-scrollbar {
  height: 8px;
}

.products-horizontal-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.products-horizontal-scroll::-webkit-scrollbar-thumb {
  background: #041b4b;
  border-radius: 10px;
}

.products-horizontal-scroll::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}

.products-horizontal-grid {
  display: flex;
  gap: 2rem;
  padding: 0.5rem;
  min-width: max-content;
}

.product-card-horizontal {
  min-width: 300px;
  max-width: 300px;
  flex: 0 0 300px;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translateY(0);
  scroll-snap-align: start;
}

.product-card-horizontal:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5rem);
}

.product-card {
  min-width: 300px;
  flex: 0 0 auto;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translateY(0);
  scroll-snap-align: start;
}

.product-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5rem);
}
.product-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5rem);
}

.product-image-container {
  position: relative;
  overflow: hidden;
  border-top-left-radius: 0.75rem; /* Updated to match card radius */
  border-top-right-radius: 0.75rem;
  width: 100%;
  background-color: #041b4b;
}

.sale-badge {
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  background-color: #dc2626;
  color: white;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  font-weight: bold;
  border-radius: 9999px;
  z-index: 10;
}

.product-image {
  width: 100%;
  height: 280px; /* Increased from 20rem (320px) to 280px for better proportion */
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image,
.product-card-horizontal:hover .product-image {
  transform: scale(1.05);
}

.product-info {
  padding: 1.75rem; /* Increased padding for better spacing */
}

.product-name {
  font-size: 1.25rem; /* Increased font size */
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem; /* Increased margin */
  transition: color 0.3s ease;
  line-height: 1.4; /* Better line height for readability */
}

.product-card:hover .product-name,
.product-card-horizontal:hover .product-name {
  color: #dc2626;
}

.product-pricing {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-price {
  font-size: 1.375rem; /* Increased font size */
  font-weight: bold;
  color: #dc2626;
}

.original-price {
  font-size: 1rem; /* Increased font size */
  color: #6b7280;
  text-decoration: line-through;
}