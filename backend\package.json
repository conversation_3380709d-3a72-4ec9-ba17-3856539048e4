{"name": "clothing-store-backend", "version": "1.0.0", "description": "Backend API for Clothing Store Website", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node seeders/seed.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["clothing", "ecommerce", "api", "backend"], "author": "Your Name", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^2.7.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.0", "sequelize": "^6.32.1"}, "devDependencies": {"nodemon": "^3.0.1"}}