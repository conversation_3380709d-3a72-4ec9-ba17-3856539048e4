import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import { Heart, Bell, ShoppingBag, ArrowLeft, Plus, Minus, CreditCard, Truck, Shield, Check } from 'lucide-react';
import '../styles/PaymentPage.css';
import Footer from './Footer';

const PaymentPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Product and order state
  const [product, setProduct] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedColor, setSelectedColor] = useState('');
  const [selectedSize, setSelectedSize] = useState('');
  
  // Payment form state
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [paymentForm, setPaymentForm] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: '',
    email: '',
    firstName: '',
    lastName: '',
    address: '',
    city: '',
    zipCode: '',
    country: 'Cambodia'
  });
  
  // Processing state
  const [isProcessing, setIsProcessing] = useState(false);
  const [orderComplete, setOrderComplete] = useState(false);

  useEffect(() => {
    if (location.state && location.state.product) {
      const receivedProduct = location.state.product;
      setProduct(receivedProduct);
      
      // Set default selections
      if (receivedProduct.colors && receivedProduct.colors.length > 0) {
        setSelectedColor(receivedProduct.colors[0]);
      }
      
      // Set default size
      if (receivedProduct.sizes && receivedProduct.sizes.length > 0) {
        setSelectedSize(receivedProduct.sizes[0]);
      } else {
        setSelectedSize('M'); // Default size
      }
    } else {
      // If no product data, redirect to home
      navigate('/');
    }
  }, [location.state, navigate]);

  const handleQuantityChange = (change) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1) {
      setQuantity(newQuantity);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setPaymentForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const calculateSubtotal = () => {
    if (!product) return 0;
    const price = product.salePrice || product.price || 0;
    return (price * quantity).toFixed(2);
  };

  const calculateSavings = () => {
    if (!product) return 0;
    const originalPrice = product.originalPrice || product.price || 0;
    const salePrice = product.salePrice || product.price || 0;
    return ((originalPrice - salePrice) * quantity).toFixed(2);
  };

  const deliveryFee = 1.25;
  const tax = 0.50;

  const calculateTotal = () => {
    const subtotal = parseFloat(calculateSubtotal());
    return (subtotal + deliveryFee + tax).toFixed(2);
  };

  const getProductPrice = () => {
    if (!product) return 0;
    return product.salePrice || product.price || 0;
  };

  const getProductOriginalPrice = () => {
    if (!product) return 0;
    return product.originalPrice || product.price || 0;
  };

  const handleSubmitOrder = async (e) => {
    e.preventDefault();
    setIsProcessing(true);
    
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false);
      setOrderComplete(true);
    }, 3000);
  };

  if (!product) {
    return (
      <div className="loading-container">
        <div className="loading">Loading payment page...</div>
      </div>
    );
  }

  if (orderComplete) {
    return (
      <div className="payment-page">
        {/* Simple Header for Success Page */}
        <div className="simple-header">
          <div className="header-container">
            <Link to="/" className="logo-link">
              <span className="logo-text">
                <span className="logo-star">★</span>
                StyleStore
              </span>
            </Link>
            <div className="header-actions">
              <input type="text" placeholder="Search" className="search-input" />
              <div className="header-icons">
                <Bell size={20} className="icon" />
                <Heart size={20} className="icon" />
                <div className="shopping-bag-container">
                  <ShoppingBag size={22} className="icon" />
                  <span className="cart-badge">0</span>
                </div>
                <Link to="/login" className="auth-link">LOGIN</Link>
                <Link to="/register" className="auth-link">REGISTER</Link>
              </div>
            </div>
          </div>
        </div>

        <div className="order-success">
          <div className="success-container">
            <div className="success-icon">
              <Check size={64} color="#10b981" />
            </div>
            <h1>Order Successful!</h1>
            <p>Thank you for your purchase. Your order has been confirmed and will be delivered soon.</p>
            <div className="order-details">
              <h3>Order Summary</h3>
              <div className="order-item">
                <img src={product.image} alt={product.name} className="order-item-image" />
                <div className="order-item-info">
                  <p><strong>{product.name}</strong></p>
                  <p>Quantity: {quantity}</p>
                  <p>Color: {selectedColor}</p>
                  <p>Size: {selectedSize}</p>
                  <p className="order-total"><strong>Total: ${calculateTotal()}</strong></p>
                </div>
              </div>
            </div>
            <div className="action-buttons">
              <button onClick={() => navigate('/')} className="continue-shopping-btn">
                Continue Shopping
              </button>
              <button onClick={() => navigate('/orders')} className="view-orders-btn">
                Track Order
              </button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="payment-page">
      {/* Simple Header - Only Logo, Search, Login, Register */}
      <div className="simple-header">
        <div className="header-container">
          <Link to="/" className="logo-link">
            <span className="logo-text">
              <span className="logo-star">★</span>
              StyleStore
            </span>
          </Link>
          <div className="header-actions">
            <input type="text" placeholder="Search products..." className="search-input" />
            <div className="header-icons">
              <Bell size={20} className="icon" />
              <Heart size={20} className="icon" />
              <div className="shopping-bag-container">
                <ShoppingBag size={22} className="icon" />
                <span className="cart-badge">0</span>
              </div>
              <Link to="/login" className="auth-link">LOGIN</Link>
              <Link to="/register" className="auth-link">REGISTER</Link>
            </div>
          </div>
        </div>
      </div>

      {/* Back Button */}
      <div className="back-navigation">
        <button onClick={() => navigate(-1)} className="back-btn">
          <ArrowLeft size={20} />
          Back to Shopping
        </button>
      </div>

      {/* Main Payment Content */}
      <div className="payment-container">
        <div className="payment-content">
          {/* Left Side - Payment Form */}
          <div className="payment-form-section">
            <div className="checkout-header">
              <h2>Secure Checkout</h2>
              <div className="trust-indicators">
                <div className="trust-item">
                  <Shield size={16} />
                  <span>Secure Payment</span>
                </div>
                <div className="trust-item">
                  <Truck size={16} />
                  <span>Fast Delivery</span>
                </div>
                <div className="trust-item">
                  <CreditCard size={16} />
                  <span>Multiple Payment Options</span>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmitOrder} className="payment-form">
              {/* Contact Information */}
              <div className="form-section">
                <h3><span className="section-number">1</span> Contact Information</h3>
                <div className="form-row">
                  <div className="form-group">
                    <label>First Name *</label>
                    <input
                      type="text"
                      name="firstName"
                      value={paymentForm.firstName}
                      onChange={handleInputChange}
                      placeholder="Enter your first name"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>Last Name *</label>
                    <input
                      type="text"
                      name="lastName"
                      value={paymentForm.lastName}
                      onChange={handleInputChange}
                      placeholder="Enter your last name"
                      required
                    />
                  </div>
                </div>
                <div className="form-group">
                  <label>Email Address *</label>
                  <input
                    type="email"
                    name="email"
                    value={paymentForm.email}
                    onChange={handleInputChange}
                    placeholder="Enter your email address"
                    required
                  />
                </div>
              </div>

              {/* Shipping Address */}
              <div className="form-section">
                <h3><span className="section-number">2</span> Shipping Address</h3>
                <div className="form-group">
                  <label>Address *</label>
                  <input
                    type="text"
                    name="address"
                    value={paymentForm.address}
                    onChange={handleInputChange}
                    placeholder="Enter your street address"
                    required
                  />
                </div>
                <div className="form-row">
                  <div className="form-group">
                    <label>City *</label>
                    <input
                      type="text"
                      name="city"
                      value={paymentForm.city}
                      onChange={handleInputChange}
                      placeholder="Enter your city"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>ZIP Code *</label>
                    <input
                      type="text"
                      name="zipCode"
                      value={paymentForm.zipCode}
                      onChange={handleInputChange}
                      placeholder="Enter ZIP code"
                      required
                    />
                  </div>
                </div>
                <div className="form-group">
                  <label>Country *</label>
                  <select
                    name="country"
                    value={paymentForm.country}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="Cambodia">Cambodia</option>
                    <option value="Thailand">Thailand</option>
                    <option value="Vietnam">Vietnam</option>
                    <option value="Malaysia">Malaysia</option>
                  </select>
                </div>
              </div>

              {/* Enhanced Payment Method */}
              <div className="form-section payment-section">
                <h3><span className="section-number">3</span> Payment Method</h3>
                <p className="payment-subtitle">Choose your preferred payment method</p>
                <div className="payment-methods-enhanced">
                  <label className={`payment-option-enhanced ${paymentMethod === 'card' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="card"
                      checked={paymentMethod === 'card'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    />
                    <div className="payment-option-content-enhanced">
                      <div className="payment-icon-container">
                        <CreditCard size={24} className="payment-method-icon" />
                      </div>
                      <div className="payment-method-info">
                        <span className="payment-method-title">Credit/Debit Card</span>
                        <span className="payment-method-subtitle">Secure payment with your card</span>
                        <div className="card-logos-enhanced">
                          <span className="card-logo visa-enhanced">VISA</span>
                          <span className="card-logo mastercard-enhanced">MC</span>
                          <span className="card-logo amex-enhanced">AMEX</span>
                        </div>
                      </div>
                      <div className="payment-check">
                        {paymentMethod === 'card' && <Check size={20} />}
                      </div>
                    </div>
                  </label>

                  <label className={`payment-option-enhanced ${paymentMethod === 'paypal' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="paypal"
                      checked={paymentMethod === 'paypal'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    />
                    <div className="payment-option-content-enhanced">
                      <div className="payment-icon-container paypal">
                        <span className="payment-icon-emoji">💳</span>
                      </div>
                      <div className="payment-method-info">
                        <span className="payment-method-title">PayPal</span>
                        <span className="payment-method-subtitle">Pay with your PayPal account</span>
                      </div>
                      <div className="payment-check">
                        {paymentMethod === 'paypal' && <Check size={20} />}
                      </div>
                    </div>
                  </label>

                  <label className={`payment-option-enhanced ${paymentMethod === 'cod' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="cod"
                      checked={paymentMethod === 'cod'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    />
                    <div className="payment-option-content-enhanced">
                      <div className="payment-icon-container cod">
                        <span className="payment-icon-emoji">💰</span>
                      </div>
                      <div className="payment-method-info">
                        <span className="payment-method-title">Cash on Delivery</span>
                        <span className="payment-method-subtitle">Pay when you receive your order</span>
                      </div>
                      <div className="payment-check">
                        {paymentMethod === 'cod' && <Check size={20} />}
                      </div>
                    </div>
                  </label>
                </div>

                {paymentMethod === 'card' && (
                  <div className="card-details">
                    <div className="form-group">
                      <label>Card Number *</label>
                      <input
                        type="text"
                        name="cardNumber"
                        value={paymentForm.cardNumber}
                        onChange={handleInputChange}
                        placeholder="1234 5678 9012 3456"
                        required={paymentMethod === 'card'}
                      />
                    </div>
                    <div className="form-row">
                      <div className="form-group">
                        <label>Expiry Date *</label>
                        <input
                          type="text"
                          name="expiryDate"
                          value={paymentForm.expiryDate}
                          onChange={handleInputChange}
                          placeholder="MM/YY"
                          required={paymentMethod === 'card'}
                        />
                      </div>
                      <div className="form-group">
                        <label>CVV *</label>
                        <input
                          type="text"
                          name="cvv"
                          value={paymentForm.cvv}
                          onChange={handleInputChange}
                          placeholder="123"
                          required={paymentMethod === 'card'}
                        />
                      </div>
                    </div>
                    <div className="form-group">
                      <label>Cardholder Name *</label>
                      <input
                        type="text"
                        name="cardName"
                        value={paymentForm.cardName}
                        onChange={handleInputChange}
                        placeholder="John Doe"
                        required={paymentMethod === 'card'}
                      />
                    </div>
                  </div>
                )}
              </div>

              <button
                type="submit"
                className="place-order-btn"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <span className="processing">
                    <span className="spinner"></span>
                    Processing Payment...
                  </span>
                ) : (
                  `Complete Order - $${calculateTotal()}`
                )}
              </button>
            </form>
          </div>

          {/* Right Side - Enhanced Order Summary */}
          <div className="order-summary-section enhanced">
            <div className="order-summary-header">
              <h2>Order Summary</h2>
              <div className="summary-badge">
                <span className="item-count">{quantity} item{quantity > 1 ? 's' : ''}</span>
              </div>
            </div>

            {/* Product Details */}
            <div className="product-summary enhanced">
              <div className="product-image-container">
                <img src={product.image} alt={product.name} className="product-image-large" />
                {product.onSale && <div className="sale-badge-summary">SALE</div>}
              </div>
              <div className="product-details-enhanced">
                <h3 className="product-name-large">{product.name}</h3>
                <div className="product-price-display">
                  <span className="current-price-large">
                    ${(product.salePrice || product.price || 0).toFixed(2)}
                  </span>
                  {product.originalPrice && product.originalPrice !== (product.salePrice || product.price) && (
                    <span className="original-price-crossed">
                      ${product.originalPrice.toFixed(2)}
                    </span>
                  )}
                </div>

                {/* Enhanced Product Options */}
                <div className="product-options-enhanced">
                  {/* Color Selection */}
                  {product.colors && product.colors.length > 0 && (
                    <div className="option-group-enhanced">
                      <label className="option-label">Color:</label>
                      <div className="color-selector">
                        {product.colors.map((color) => (
                          <button
                            key={color}
                            type="button"
                            className={`color-option ${selectedColor === color ? 'selected' : ''}`}
                            onClick={() => setSelectedColor(color)}
                            style={{ backgroundColor: color === 'white' ? '#f8f9fa' : color }}
                            title={color.charAt(0).toUpperCase() + color.slice(1)}
                          >
                            {selectedColor === color && <Check size={12} />}
                          </button>
                        ))}
                      </div>
                      <span className="selected-option">{selectedColor.charAt(0).toUpperCase() + selectedColor.slice(1)}</span>
                    </div>
                  )}

                  {/* Size Selection */}
                  <div className="option-group-enhanced">
                    <label className="option-label">Size:</label>
                    <div className="size-selector">
                      {(product.sizes || ['XS', 'S', 'M', 'L', 'XL']).map((size) => (
                        <button
                          key={size}
                          type="button"
                          className={`size-option ${selectedSize === size ? 'selected' : ''}`}
                          onClick={() => setSelectedSize(size)}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Enhanced Quantity Controls */}
                  <div className="option-group-enhanced quantity-section">
                    <label className="option-label">
                      Quantity (${(product.salePrice || product.price || 0).toFixed(2)} each):
                    </label>
                    <div className="quantity-controls-enhanced">
                      <button
                        type="button"
                        className="quantity-btn decrease"
                        onClick={() => handleQuantityChange(-1)}
                        disabled={quantity <= 1}
                      >
                        <Minus size={18} />
                      </button>
                      <div className="quantity-display">
                        <span className="quantity-number">{quantity}</span>
                      </div>
                      <button
                        type="button"
                        className="quantity-btn increase"
                        onClick={() => handleQuantityChange(1)}
                      >
                        <Plus size={18} />
                      </button>
                    </div>
                    <div className="item-total-display">
                      <span className="item-total-label">Item Total ({quantity} × ${(product.salePrice || product.price || 0).toFixed(2)}):</span>
                      <span className="item-total-price">${calculateSubtotal()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Price Breakdown */}
            <div className="price-breakdown-enhanced">
              <h4 className="breakdown-title">Price Details</h4>

              <div className="price-row-enhanced">
                <span className="price-label">Subtotal ({quantity} item{quantity > 1 ? 's' : ''})</span>
                <span className="price-value">${calculateSubtotal()}</span>
              </div>

              {parseFloat(calculateSavings()) > 0 && (
                <div className="price-row-enhanced savings">
                  <span className="price-label savings-label">
                    <span className="savings-icon">💰</span>
                    You Save
                  </span>
                  <span className="price-value savings-value">-${calculateSavings()}</span>
                </div>
              )}

              <div className="price-row-enhanced">
                <span className="price-label">
                  <span className="delivery-icon">🚚</span>
                  Delivery
                </span>
                <span className="price-value">${deliveryFee.toFixed(2)}</span>
              </div>

              <div className="price-row-enhanced">
                <span className="price-label">Tax</span>
                <span className="price-value">${tax.toFixed(2)}</span>
              </div>

              <div className="price-divider"></div>

              <div className="price-row-enhanced total-row">
                <span className="price-label total-label">Total Amount</span>
                <span className="price-value total-value">${calculateTotal()}</span>
              </div>

              <div className="savings-summary">
                {parseFloat(calculateSavings()) > 0 && (
                  <p className="total-savings">
                    🎉 You're saving ${calculateSavings()} on this order!
                  </p>
                )}
              </div>
            </div>

            {/* Delivery Info */}
            <div className="delivery-info">
              <h4>Delivery Information</h4>
              <div className="delivery-option">
                <div className="delivery-icon">📦</div>
                <div>
                  <p><strong>Standard Delivery</strong></p>
                  <p>3-5 business days</p>
                </div>
              </div>
              <div className="benefits">
                <p>✅ Free returns within 30 days</p>
                <p>✅ Secure packaging</p>
                <p>✅ Track your order</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default PaymentPage;
